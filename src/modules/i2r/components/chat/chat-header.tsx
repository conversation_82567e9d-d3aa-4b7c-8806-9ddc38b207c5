import React from 'react';
import { SparklesIcon } from '@heroicons/react/24/outline';
import { useTranslations } from 'next-intl';
import { IChatHeaderProps } from './types';

const ChatHeader = ({ 
  title, 
  showIcon = true, 
  className = '' 
}: IChatHeaderProps) => {
  const i2rConstants = useTranslations('I2R.homepage');
  
  const displayTitle = title || i2rConstants('title');

  return (
    <div className={`flex items-center gap-4 ${className}`}>
      {showIcon && (
        <div className="h-fit w-fit rounded-full border-1 border-secondary-neutral-200 bg-white p-1">
          <SparklesIcon className="h-5 w-5 text-primary-teal-600" />
        </div>
      )}
      <p className="label-m text-secondary-neutral-900">{displayTitle}</p>
    </div>
  );
};

export default ChatHeader;
