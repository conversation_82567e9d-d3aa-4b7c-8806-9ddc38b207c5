import React, { ChangeEvent, useCallback } from 'react';
import Textarea from '@/components/textarea';
import { ArrowUpTrayIcon, PaperAirplaneIcon } from '@heroicons/react/24/outline';
import { IChatInputProps } from './types';
import { Controller, useForm } from 'react-hook-form';
import { useTranslations } from 'next-intl';
import Image from 'next/image';

const ChatInput = ({
  value,
  onChange,
  onSubmit,
  placeholder = 'Enter your message here...',
  disabled = false,
  isLoading = false,
  isInvalid = false,
  errorMessage,
}: IChatInputProps) => {
  const { control } = useForm();
  const homepageConstants = useTranslations('I2R.homepage');

  // Handle input change
  const handleInputChange = useCallback((e: ChangeEvent<HTMLInputElement>) => {
    onChange(e.target.value);
  }, [onChange]);

  // Handle key events
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
    }
  };

  const handleKeyUp = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      onSubmit();
    }
  };

  // Handle send button click
  const handleSendClick = useCallback(() => {
    if (!disabled && !isLoading && value.trim()) {
      onSubmit();
    }
  }, [disabled, isLoading, value, onSubmit]);

  const canSend = !disabled && !isLoading && value.trim();

  return (
    // <div className="sticky bottom-0">
    //   <Textarea
    //     placeholder={placeholder}
    //     value={value}
    //     isInvalid={isInvalid}
    //     isDisabled={disabled || isLoading}
    //     errorMessage={errorMessage}
    //     endContent={
    //       <PaperAirplaneIcon
    //         className={`h-6 w-6 cursor-pointer transition-colors ${
    //           canSend
    //             ? 'text-secondary-neutral-600 hover:text-primary-teal-600'
    //             : 'text-secondary-neutral-300'
    //         }`}
    //         onClick={handleSendClick}
    //       />
    //     }
    //     isRequired
    //     onChange={handleInputChange}
    //     onKeyDown={handleKeyDown}
    //     onKeyUp={handleKeyUp}
    //   />
    // </div>
    <div className="flex items-end rounded-xl border border-gray-200 bg-white">
      <Controller
        name="idea"
        control={control}
        rules={{ required: homepageConstants('inputs.idea.error') }}
        render={({ field }) => (
          <div className="relative w-full">
            <Textarea
              {...field}
              placeholder={homepageConstants('inputs.idea.placeholder')}
              className="rounded-xl border-none w-full resize-none focus:ring-0"
              // isInvalid={!!errors.idea}
              // errorMessage={errors.idea?.message}
              onChange={handleInputChange}
              onKeyDown={handleKeyDown}
              onKeyUp={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  // handleSubmit(handleIdeaSubmission)();
                }
              }}
            />
            {/* Left icons */}
            <div className="absolute left-4 top-1/2 -translate-y-1/2 flex items-center gap-2">
              <Image src="/icons/jira.svg" alt="jira logo" width={20} height={20} />
              <ArrowUpTrayIcon className="h-6 w-6 text-primary-teal-600" />
            </div>
            {/* Right icon */}
            <div className="absolute right-4 top-1/2 -translate-y-1/2 flex items-center">
              <PaperAirplaneIcon
                className={`h-6 w-6 cursor-pointer transition-colors ${canSend
                    ? 'text-secondary-neutral-600 hover:text-primary-teal-600'
                    : 'text-secondary-neutral-300'
                  }`}
                onClick={handleSendClick}
              />
            </div>
          </div>
        )}
      />
    </div>
  );
};

export default ChatInput;
