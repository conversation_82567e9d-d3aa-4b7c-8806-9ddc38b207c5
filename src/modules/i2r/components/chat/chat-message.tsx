import React from 'react';
import UserAvatar from '@/components/user-avatar';
import { SparklesIcon } from '@heroicons/react/24/outline';
import { useTranslations } from 'next-intl';
import { IChatMessage } from './types';

interface IChatMessageProps {
  message: IChatMessage;
}

const ChatMessage = ({ message }: IChatMessageProps) => {
  const chatConstants = useTranslations('Platform.chat');

  if (message.isUser) {
    return (
      <div className="flex flex-col gap-4 rounded-lg border p-4">
        <div className="flex items-center gap-3">
          <UserAvatar sizeClassName="w-8 h-8" />
          <p className="label-m text-secondary-neutral-900">{chatConstants('you')}</p>
        </div>
        <div className="label-s text-secondary-neutral-500">
          <p className="whitespace-pre-wrap">{message.content}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-4 rounded-lg border p-4">
      <div className="flex items-center gap-3">
        <div className="h-fit w-fit rounded-full border-1 border-secondary-neutral-200 bg-white p-1">
          <SparklesIcon className="h-5 w-5 text-primary-teal-600" />
        </div>
        <p className="label-m text-secondary-neutral-900">AI Assistant</p>
      </div>
      <div className="label-s text-secondary-neutral-500">
        {message.isLoading ? (
          <div className="flex items-center gap-2">
            <div className="h-2 w-2 animate-pulse rounded-full bg-primary-teal-600"></div>
            <div className="h-2 w-2 animate-pulse rounded-full bg-primary-teal-600 animation-delay-200"></div>
            <div className="h-2 w-2 animate-pulse rounded-full bg-primary-teal-600 animation-delay-400"></div>
            <span className="ml-2">{chatConstants('loading')}</span>
          </div>
        ) : message.error ? (
          <p className="text-red-500">{message.error}</p>
        ) : (
          <p className="whitespace-pre-wrap">{message.content}</p>
        )}
      </div>
    </div>
  );
};

export default ChatMessage;
