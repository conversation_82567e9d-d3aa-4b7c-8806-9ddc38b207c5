// Chat message interface
export interface IChatMessage {
  id: string;
  content: string;
  isUser: boolean;
  timestamp: Date;
  isLoading?: boolean;
  error?: string;
}

// Chat component props interface
export interface IChatProps {
  chatMessages: IChatMessage[];
  onSendMessage: (message: string) => void;
  isLoading?: boolean;
  placeholder?: string;
  disabled?: boolean;
  showHeader?: boolean;
  headerTitle?: string;
  className?: string;
}

// Chat message component props
export interface IChatMessageProps {
  message: IChatMessage;
}

// Chat input props
export interface IChatInputProps {
  value: string;
  onChange: (value: string) => void;
  onSubmit: () => void;
  placeholder?: string;
  disabled?: boolean;
  isLoading?: boolean;
  isInvalid?: boolean;
  errorMessage?: string;
}

// Chat header props
export interface IChatHeaderProps {
  title?: string;
  showIcon?: boolean;
  className?: string;
}
