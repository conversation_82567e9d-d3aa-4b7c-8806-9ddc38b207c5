import React from 'react';
import Layout, { LayoutType } from '@/components/layout';
import I2RChat from './index';
import { useChat } from '../../utils/hooks/use-chat';
import { I2RPollingProvider } from '../../contexts/polling.context';
import ModuleSidebar from '@/modules/platform/components/module-sidebar';
import { I2R_BREADCRUMBS } from '@/modules/platform/constants/module-sidebar';
import { Modules } from '@/modules/platform/interfaces/modules';
import { useTranslations } from 'next-intl';
import { useRouter } from 'next/router';
import { useRequestHistory } from '@/modules/platform/utils/hooks/request-history';

/**
 * Integration example showing how to replace the existing I2R output page
 * with the new chat component
 * 
 * This is an example of how you could modify src/pages/i2r/[chat-id]/index.tsx
 */
const I2RChatIntegrationExample = () => {
  const router = useRouter();
  const { 'chat-id': chatId } = router.query;

  const commonConstants = useTranslations('Common');
  const i2rConstants = useTranslations('I2R.homepage');

  const { activeRequests, archivedRequests } = useRequestHistory(Modules.I2R);

  // Use the chat hook for managing chat state
  const {
    messages,
    sendMessage,
    isLoading,
    error,
    addMessage,
  } = useChat({
    chatId: chatId as string,
    onMessageSent: (message) => {
      console.log('User sent message:', message);
      // You can add analytics or other side effects here
    },
    onMessageReceived: (response) => {
      console.log('AI responded:', response);
      // Handle AI response, maybe trigger other actions
    },
    onError: (error) => {
      console.error('Chat error:', error);
      // Handle errors, show notifications, etc.
    },
  });

  const onCreateNewRequest = () => router.push(`/${Modules.I2R.toLocaleLowerCase()}`);

  // You could add initial messages or system messages
  React.useEffect(() => {
    if (messages.length === 0) {
      // Add a welcome message when chat is first loaded
      addMessage({
        content: "Hello! I'm your I2R assistant. I can help you convert your ideas into detailed requirements. What would you like to work on today?",
        isUser: false,
        timestamp: new Date(),
      });
    }
  }, [messages.length, addMessage]);

  return (
    <I2RPollingProvider>
      <div className="flex h-full w-full gap-4">
        <ModuleSidebar
          breadcrumbItems={I2R_BREADCRUMBS.map((item) => ({
            ...item,
            children: commonConstants(item.children),
          }))}
          tabItems={[
            {
              title: i2rConstants('newRequest'),
              onClick: onCreateNewRequest,
            },
          ]}
          activeRequests={activeRequests}
          archivedRequests={archivedRequests}
        />
        
        <div className="flex w-full flex-col gap-6 rounded-lg border p-4">
          {/* Chat Interface */}
          <div className="flex h-full flex-col justify-between gap-4 overflow-y-auto rounded-xl bg-secondary-neutral-50 p-4">
            <I2RChat
              chatMessages={messages}
              onSendMessage={sendMessage}
              isLoading={isLoading}
              placeholder="Describe your idea or requirements..."
              showHeader={true}
              headerTitle={i2rConstants('title')}
              className="h-full"
            />
            
            {/* Error display */}
            {error && (
              <div className="rounded-lg bg-red-50 border border-red-200 p-3">
                <p className="text-sm text-red-600">
                  Error: {error}
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </I2RPollingProvider>
  );
};

// Export with layout wrapper
export default Layout(I2RChatIntegrationExample, LayoutType.MAIN);

// Required for i18n
I2RChatIntegrationExample.messages = ['I2R', 'Platform', 'Common'];

/**
 * To use this integration:
 * 
 * 1. Replace the content of src/pages/i2r/[chat-id]/index.tsx with this component
 * 2. Update the API integration in src/modules/i2r/utils/hooks/use-chat.ts
 * 3. Connect to your actual backend endpoints
 * 4. Add any additional features like file uploads, message history, etc.
 * 
 * Key differences from the original:
 * - Replaces the complex I2ROutput component with a simple chat interface
 * - Uses the useChat hook for state management
 * - Provides a cleaner, more maintainable structure
 * - Easier to extend with new features
 * - Better separation of concerns
 */
