# I2R Chat Component

A reusable chat interface component designed for the I2R (Idea to Requirements) module. This component provides a clean, modern chat interface similar to the Figma design specifications.

## Features

- 🎨 **Modern UI**: Clean design matching the existing design system
- 📱 **Responsive**: Works on different screen sizes
- ♿ **Accessible**: Proper ARIA labels and keyboard navigation
- 🔧 **Modular**: Broken down into smaller, reusable components
- 🎯 **TypeScript**: Fully typed for better developer experience
- 🔄 **Real-time**: Auto-scroll to new messages
- ⌨️ **Keyboard Support**: Enter to send, Shift+Enter for new line

## Components

### Main Components

1. **I2RChat** - Main chat interface component
2. **ChatMessage** - Individual message component (user/AI)
3. **ChatInput** - Input area with send functionality
4. **ChatHeader** - Header with title and icon

### Hook

- **useChat** - Custom hook for managing chat state and API calls

## Usage

### Basic Usage

```tsx
import I2RChat from '@/modules/i2r/components/chat';
import { useChat } from '@/modules/i2r/utils/hooks/use-chat';

const MyComponent = () => {
  const { messages, sendMessage, isLoading } = useChat({
    chatId: 'your-chat-id',
  });

  return (
    <I2RChat
      chatMessages={messages}
      onSendMessage={sendMessage}
      isLoading={isLoading}
      placeholder="Type your message..."
    />
  );
};
```

### Advanced Usage

```tsx
import I2RChat from '@/modules/i2r/components/chat';
import { useChat } from '@/modules/i2r/utils/hooks/use-chat';

const AdvancedChatComponent = () => {
  const {
    messages,
    sendMessage,
    isLoading,
    error,
    clearMessages,
    addMessage,
  } = useChat({
    chatId: 'chat-123',
    onMessageSent: (message) => console.log('Sent:', message),
    onMessageReceived: (response) => console.log('Received:', response),
    onError: (error) => console.error('Error:', error),
  });

  return (
    <div className="h-full flex flex-col">
      <I2RChat
        chatMessages={messages}
        onSendMessage={sendMessage}
        isLoading={isLoading}
        placeholder="Ask me about your requirements..."
        disabled={false}
        showHeader={true}
        headerTitle="Requirements Assistant"
        className="flex-1"
      />
      
      {error && (
        <div className="p-2 text-red-500 text-sm">
          Error: {error}
        </div>
      )}
    </div>
  );
};
```

## Props

### I2RChat Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `chatMessages` | `IChatMessage[]` | Required | Array of chat messages |
| `onSendMessage` | `(message: string) => void` | Required | Callback when user sends a message |
| `isLoading` | `boolean` | `false` | Whether the chat is loading |
| `placeholder` | `string` | `'Enter your message here...'` | Input placeholder text |
| `disabled` | `boolean` | `false` | Whether the input is disabled |
| `showHeader` | `boolean` | `true` | Whether to show the header |
| `headerTitle` | `string` | `undefined` | Custom header title |
| `className` | `string` | `''` | Additional CSS classes |

### IChatMessage Interface

```tsx
interface IChatMessage {
  id: string;
  content: string;
  isUser: boolean;
  timestamp: Date;
  isLoading?: boolean;
  error?: string;
}
```

### useChat Hook Options

```tsx
interface IUseChatProps {
  chatId?: string;
  onMessageSent?: (message: string) => void;
  onMessageReceived?: (message: string) => void;
  onError?: (error: string) => void;
}
```

## Integration with Existing Pages

To integrate this component into existing I2R pages:

1. **Replace existing chat UI** in your page component
2. **Use the useChat hook** for state management
3. **Connect to your API** by modifying the `sendChatMessage` function in the hook

### Example Integration

```tsx
// In your page component (e.g., src/pages/i2r/[chat-id]/index.tsx)
import I2RChat from '@/modules/i2r/components/chat';
import { useChat } from '@/modules/i2r/utils/hooks/use-chat';

const I2ROutputPage = () => {
  const router = useRouter();
  const { 'chat-id': chatId } = router.query;

  const { messages, sendMessage, isLoading } = useChat({
    chatId: chatId as string,
  });

  return (
    <Layout>
      <div className="flex h-full w-full gap-4">
        <Sidebar />
        <div className="flex w-full flex-col gap-6 rounded-lg border p-4">
          <div className="flex h-full flex-col justify-between gap-4 overflow-y-auto rounded-xl bg-secondary-neutral-50 p-4">
            <I2RChat
              chatMessages={messages}
              onSendMessage={sendMessage}
              isLoading={isLoading}
              headerTitle="I2R Assistant"
              className="h-full"
            />
          </div>
        </div>
      </div>
    </Layout>
  );
};
```

## Customization

### Styling

The component uses Tailwind CSS classes and follows the existing design system. You can customize:

- Colors by modifying the CSS classes
- Spacing by adjusting gap and padding classes
- Typography by changing the label classes

### API Integration

To connect to your actual API:

1. Modify the `sendChatMessage` function in `use-chat.ts`
2. Update the API endpoint and request format
3. Handle different response types as needed

```tsx
// Example API integration
const sendChatMessage = async (chatId: string, message: string): Promise<string> => {
  const response = await fetch(`/api/chat/${chatId}/messages`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ message }),
  });
  
  if (!response.ok) {
    throw new Error('Failed to send message');
  }
  
  const data = await response.json();
  return data.response;
};
```

## Development

### File Structure

```
src/modules/i2r/components/chat/
├── index.tsx              # Main chat component
├── chat-message.tsx       # Message component
├── chat-input.tsx         # Input component
├── chat-header.tsx        # Header component
├── chat-example.tsx       # Example usage
├── types.ts              # TypeScript interfaces
└── README.md             # This file

src/modules/i2r/utils/hooks/
└── use-chat.ts           # Chat state management hook
```

### Testing

The component is designed to be easily testable:

- Each sub-component can be tested independently
- The useChat hook can be tested separately
- Mock the API calls for unit tests

### Contributing

When modifying this component:

1. Keep the modular structure
2. Follow the existing TypeScript patterns
3. Update this README if adding new features
4. Ensure accessibility standards are maintained
