import React, { useCallback, useEffect, useRef, useState } from 'react';
import ChatMessage from './chat-message';
import ChatInput from './chat-input';
import ChatHeader from './chat-header';
import { IChatProps, IChatMessage } from './types';

// Export types for external use
export type { IChatMessage, IChatProps };

// Main chat component
const I2RChat = ({
  chatMessages,
  onSendMessage,
  isLoading = false,
  placeholder = 'Enter your message here...',
  disabled = false,
  showHeader = true,
  headerTitle,
  className = '',
}: IChatProps) => {
  const [userInput, setUserInput] = useState<string>('');
  const [isUserInputInvalid, setIsUserInputInvalid] = useState<boolean>(false);
  const endOfMessagesRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages are added
  useEffect(() => {
    endOfMessagesRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [chatMessages]);

  // Handle input change
  const handleInputChange = useCallback((value: string) => {
    setUserInput(value);

    // Reset invalid state when user starts typing
    if (isUserInputInvalid && value.trim()) {
      setIsUserInputInvalid(false);
    }
  }, [isUserInputInvalid]);

  // Handle message submission
  const handleSubmit = useCallback(() => {
    const trimmedInput = userInput.trim();

    if (!trimmedInput) {
      setIsUserInputInvalid(true);
      return;
    }

    if (disabled || isLoading) {
      return;
    }

    // Call the parent's send message handler
    onSendMessage(trimmedInput);

    // Clear the input
    setUserInput('');
    setIsUserInputInvalid(false);
  }, [userInput, disabled, isLoading, onSendMessage]);

  // Error message for input
  const getErrorMessage = () => {
    if (isUserInputInvalid) {
      return 'Please enter a message';
    }
    if (isLoading) {
      return 'Please wait for the response';
    }
    return undefined;
  };

  return (
    <div className={`flex h-full flex-col gap-4 ${className}`}>

      {/* Chat messages container */}
      <div className="flex-1 overflow-y-auto">
        <div className="flex flex-col gap-4">
          {chatMessages.map((message) => (
            <ChatMessage key={message.id} message={message} />
          ))}
          <div ref={endOfMessagesRef} />
        </div>
      </div>

      {/* Input area */}
      <ChatInput
        value={userInput}
        onChange={handleInputChange}
        onSubmit={handleSubmit}
        placeholder={placeholder}
        disabled={disabled}
        isLoading={isLoading}
        isInvalid={isUserInputInvalid}
        errorMessage={getErrorMessage()}
      />
    </div>
  );
};

export default I2RChat;
