import React from 'react';
import I2RChat from './index';
import { useChat } from '../../utils/hooks/use-chat';

interface IChatExampleProps {
  chatId: string;
  className?: string;
}

/**
 * Example component showing how to use the I2RChat component
 * This demonstrates the integration with the useChat hook
 */
const ChatExample = ({ chatId, className }: IChatExampleProps) => {
  // Use the chat hook to manage chat state
  const {
    messages,
    sendMessage,
    isLoading,
    error,
    clearMessages,
  } = useChat({
    chatId,
    onMessageSent: (message) => {
      console.log('Message sent:', message);
    },
    onMessageReceived: (message) => {
      console.log('Message received:', message);
    },
    onError: (error) => {
      console.error('Chat error:', error);
    },
  });

  return (
    <div className={`h-full ${className}`}>
      <I2RChat
        chatMessages={messages}
        onSendMessage={sendMessage}
        isLoading={isLoading}
        placeholder="Ask me anything about your requirements..."
        showHeader={true}
        headerTitle="I2R Assistant"
        className="h-full"
      />
      
      {/* Optional: Add a clear button for development */}
      {process.env.NODE_ENV === 'development' && messages.length > 0 && (
        <button
          onClick={clearMessages}
          className="mt-2 text-sm text-secondary-neutral-500 hover:text-secondary-neutral-700"
        >
          Clear Chat (Dev Only)
        </button>
      )}
    </div>
  );
};

export default ChatExample;
