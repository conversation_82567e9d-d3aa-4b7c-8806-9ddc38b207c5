import { useState, useCallback } from 'react';
import { useMutation } from '@tanstack/react-query';
import { IChatMessage } from '../../components/chat/types';

interface IUseChatProps {
  chatId?: string;
  onMessageSent?: (message: string) => void;
  onMessageReceived?: (message: string) => void;
  onError?: (error: string) => void;
}

interface IUseChatReturn {
  messages: IChatMessage[];
  sendMessage: (content: string) => void;
  isLoading: boolean;
  error: string | null;
  clearMessages: () => void;
  addMessage: (message: Omit<IChatMessage, 'id'>) => void;
}

// Mock API function - replace with actual API call
const sendChatMessage = async (chatId: string, message: string): Promise<string> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Mock response
  return `This is a response to: "${message}"`;
};

export const useChat = ({
  chatId,
  onMessageSent,
  onMessageReceived,
  onError,
}: IUseChatProps = {}): IUseChatReturn => {
  const [messages, setMessages] = useState<IChatMessage[]>([]);
  const [error, setError] = useState<string | null>(null);

  // Mutation for sending messages
  const sendMessageMutation = useMutation({
    mutationFn: (message: string) => {
      if (!chatId) {
        throw new Error('Chat ID is required');
      }
      return sendChatMessage(chatId, message);
    },
    onSuccess: (response, variables) => {
      // Add AI response message
      const aiMessage: IChatMessage = {
        id: `ai-${Date.now()}`,
        content: response,
        isUser: false,
        timestamp: new Date(),
        isLoading: false,
      };

      setMessages(prev => prev.map(msg => 
        msg.isLoading ? { ...msg, isLoading: false } : msg
      ).concat(aiMessage));

      onMessageReceived?.(response);
      setError(null);
    },
    onError: (error: Error) => {
      const errorMessage = error.message || 'Failed to send message';
      
      // Update loading message to show error
      setMessages(prev => prev.map(msg => 
        msg.isLoading ? { ...msg, isLoading: false, error: errorMessage } : msg
      ));

      setError(errorMessage);
      onError?.(errorMessage);
    },
  });

  // Function to send a message
  const sendMessage = useCallback((content: string) => {
    if (!content.trim()) return;

    // Add user message
    const userMessage: IChatMessage = {
      id: `user-${Date.now()}`,
      content: content.trim(),
      isUser: true,
      timestamp: new Date(),
    };

    // Add loading AI message
    const loadingMessage: IChatMessage = {
      id: `ai-loading-${Date.now()}`,
      content: '',
      isUser: false,
      timestamp: new Date(),
      isLoading: true,
    };

    setMessages(prev => [...prev, userMessage, loadingMessage]);
    onMessageSent?.(content);

    // Send message to API
    sendMessageMutation.mutate(content);
  }, [sendMessageMutation, onMessageSent]);

  // Function to clear all messages
  const clearMessages = useCallback(() => {
    setMessages([]);
    setError(null);
  }, []);

  // Function to add a message manually
  const addMessage = useCallback((message: Omit<IChatMessage, 'id'>) => {
    const newMessage: IChatMessage = {
      ...message,
      id: `manual-${Date.now()}`,
    };
    setMessages(prev => [...prev, newMessage]);
  }, []);

  return {
    messages,
    sendMessage,
    isLoading: sendMessageMutation.isPending,
    error,
    clearMessages,
    addMessage,
  };
};
